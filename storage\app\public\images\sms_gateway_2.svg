<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="370" xmlns="http://www.w3.org/2000/svg">
<defs>
<style>
.modern-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.text-modern { font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; }
</style>
<linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
<stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
</linearGradient>
<linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
<stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
</linearGradient>
<linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
<stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
</linearGradient>
<filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
<feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.1"/>
</filter>
</defs>

  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#backgroundGradient)" rx="12"/>
  
  <!-- Header -->
  <rect x="20" y="20" width="560" height="50" fill="url(#primaryGradient)" rx="8" filter="url(#dropShadow)"/>
  <text x="300" y="50" text-anchor="middle" class="text-modern" font-size="18" font-weight="600" fill="white">SMS Testing Dashboard</text>
  
  <!-- Test Form -->
  <rect x="40" y="90" width="520" height="180" fill="white" rx="8" stroke="#e2e8f0" stroke-width="2" filter="url(#dropShadow)"/>
  <text x="60" y="115" class="text-modern" font-size="16" font-weight="600" fill="#1e293b">Send Test SMS</text>
  
  <!-- Phone Number Field -->
  <text x="60" y="140" class="text-modern" font-size="12" font-weight="500" fill="#475569">Phone Number:</text>
  <rect x="60" y="145" width="200" height="25" fill="#f8fafc" rx="4" stroke="#cbd5e1" stroke-width="1"/>
  <text x="70" y="160" class="text-modern" font-size="11" fill="#64748b">+1234567890</text>
  
  <!-- Message Field -->
  <text x="60" y="185" class="text-modern" font-size="12" font-weight="500" fill="#475569">Message:</text>
  <rect x="60" y="190" width="480" height="40" fill="#f8fafc" rx="4" stroke="#cbd5e1" stroke-width="1"/>
  <text x="70" y="205" class="text-modern" font-size="11" fill="#64748b">Test message from SMS Gateway</text>
  <text x="70" y="220" class="text-modern" font-size="11" fill="#64748b">Configuration successful!</text>
  
  <!-- Send Button -->
  <rect x="60" y="240" width="100" height="25" fill="url(#secondaryGradient)" rx="4" filter="url(#dropShadow)"/>
  <text x="110" y="255" text-anchor="middle" class="text-modern" font-size="12" font-weight="600" fill="white">Send Test</text>
  
  <!-- Status -->
  <circle cx="480" cy="252" r="8" fill="#10b981"/>
  <text x="500" y="257" class="text-modern" font-size="12" font-weight="500" fill="#059669">Message Sent Successfully</text>
  
  <!-- Delivery Report -->
  <rect x="40" y="290" width="520" height="60" fill="white" rx="8" stroke="#e2e8f0" stroke-width="2" filter="url(#dropShadow)"/>
  <text x="60" y="315" class="text-modern" font-size="14" font-weight="600" fill="#1e293b">Delivery Report</text>
  <text x="60" y="335" class="text-modern" font-size="12" fill="#059669">✓ Message delivered at 2024-01-15 10:30:45</text>

</svg>