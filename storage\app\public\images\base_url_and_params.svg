<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="390" xmlns="http://www.w3.org/2000/svg">
<defs>
<style>
.modern-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.text-modern { font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; }
</style>
<linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
<stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
</linearGradient>
<linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
<stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
</linearGradient>
<linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
<stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
</linearGradient>
<filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
<feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.1"/>
</filter>
</defs>

  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#backgroundGradient)" rx="12"/>
  
  <!-- Header -->
  <rect x="20" y="20" width="560" height="50" fill="url(#primaryGradient)" rx="8" filter="url(#dropShadow)"/>
  <text x="300" y="50" text-anchor="middle" class="text-modern" font-size="18" font-weight="600" fill="white">API Configuration</text>
  
  <!-- Base URL Section -->
  <rect x="40" y="90" width="520" height="60" fill="white" rx="8" stroke="#e2e8f0" stroke-width="2" filter="url(#dropShadow)"/>
  <text x="60" y="115" class="text-modern" font-size="14" font-weight="600" fill="#1e293b">Base URL:</text>
  <rect x="60" y="125" width="480" height="20" fill="#f1f5f9" rx="4" stroke="#cbd5e1" stroke-width="1"/>
  <text x="70" y="138" class="text-modern" font-size="12" fill="#64748b">https://api.twilio.com/2010-04-01/Accounts/</text>
  
  <!-- Parameters Section -->
  <rect x="40" y="170" width="520" height="100" fill="white" rx="8" stroke="#e2e8f0" stroke-width="2" filter="url(#dropShadow)"/>
  <text x="60" y="195" class="text-modern" font-size="14" font-weight="600" fill="#1e293b">Parameters:</text>
  
  <!-- Parameter rows -->
  <text x="70" y="215" class="text-modern" font-size="12" fill="#475569">To: {only_mobile_number}</text>
  <text x="70" y="235" class="text-modern" font-size="12" fill="#475569">From: +1234567890</text>
  <text x="70" y="255" class="text-modern" font-size="12" fill="#475569">Body: {message}</text>
  
  <!-- Headers Section -->
  <rect x="40" y="290" width="520" height="80" fill="white" rx="8" stroke="#e2e8f0" stroke-width="2" filter="url(#dropShadow)"/>
  <text x="60" y="315" class="text-modern" font-size="14" font-weight="600" fill="#1e293b">Headers:</text>
  <text x="70" y="335" class="text-modern" font-size="12" fill="#475569">Content-Type: application/x-www-form-urlencoded</text>
  <text x="70" y="355" class="text-modern" font-size="12" fill="#475569">Authorization: Basic {base64_encoded_credentials}</text>

</svg>