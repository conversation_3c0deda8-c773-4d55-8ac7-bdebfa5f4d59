<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="280" xmlns="http://www.w3.org/2000/svg">
<defs>
<style>
.modern-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.text-modern { font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; }
</style>
<linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
<stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
</linearGradient>
<linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
<stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
</linearGradient>
<linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
<stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
</linearGradient>
<filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
<feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.1"/>
</filter>
</defs>

  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#backgroundGradient)" rx="12"/>
  
  <!-- Header -->
  <rect x="20" y="20" width="560" height="60" fill="url(#primaryGradient)" rx="8" filter="url(#dropShadow)"/>
  <text x="300" y="55" text-anchor="middle" class="text-modern" font-size="20" font-weight="600" fill="white">SMS Provider Dashboard</text>
  
  <!-- Provider Cards -->
  <rect x="40" y="100" width="160" height="120" fill="white" rx="8" stroke="#e2e8f0" stroke-width="2" filter="url(#dropShadow)"/>
  <circle cx="120" cy="140" r="20" fill="url(#secondaryGradient)"/>
  <text x="120" y="145" text-anchor="middle" class="text-modern" font-size="12" font-weight="600" fill="white">T</text>
  <text x="120" y="175" text-anchor="middle" class="text-modern" font-size="14" font-weight="600" fill="#1e293b">Twilio</text>
  <text x="120" y="195" text-anchor="middle" class="text-modern" font-size="12" fill="#64748b">Reliable SMS API</text>
  
  <rect x="220" y="100" width="160" height="120" fill="white" rx="8" stroke="#e2e8f0" stroke-width="2" filter="url(#dropShadow)"/>
  <circle cx="300" cy="140" r="20" fill="url(#secondaryGradient)"/>
  <text x="300" y="145" text-anchor="middle" class="text-modern" font-size="12" font-weight="600" fill="white">N</text>
  <text x="300" y="175" text-anchor="middle" class="text-modern" font-size="14" font-weight="600" fill="#1e293b">Nexmo</text>
  <text x="300" y="195" text-anchor="middle" class="text-modern" font-size="12" fill="#64748b">Global Coverage</text>
  
  <rect x="400" y="100" width="160" height="120" fill="white" rx="8" stroke="#e2e8f0" stroke-width="2" filter="url(#dropShadow)"/>
  <circle cx="480" cy="140" r="20" fill="url(#secondaryGradient)"/>
  <text x="480" y="145" text-anchor="middle" class="text-modern" font-size="12" font-weight="600" fill="white">TL</text>
  <text x="480" y="175" text-anchor="middle" class="text-modern" font-size="14" font-weight="600" fill="#1e293b">TextLocal</text>
  <text x="480" y="195" text-anchor="middle" class="text-modern" font-size="12" fill="#64748b">Cost Effective</text>
  
  <!-- Footer -->
  <text x="300" y="260" text-anchor="middle" class="text-modern" font-size="16" font-weight="500" fill="#475569">Choose Your SMS Provider</text>

</svg>